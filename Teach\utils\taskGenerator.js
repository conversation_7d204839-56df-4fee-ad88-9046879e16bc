// Task Generator - Can be extended to use external APIs
const fs = require('fs').promises;

// Sample additional tasks that could come from an API
const additionalTasks = [
  {
    id: 13,
    title: "FizzBuzz Challenge",
    description: "Write a function that prints numbers 1-100, but replaces multiples of 3 with 'Fizz', multiples of 5 with 'Buzz', and multiples of both with 'FizzBuzz'.",
    difficulty: "intermediate",
    category: "algorithms",
    instructions: "Create a function called 'fizzBuzz' that returns an array with the FizzBuzz sequence from 1 to n.",
    starterCode: "function fizzBuzz(n) {\n  // Write your code here\n  \n}",
    solution: "function fizzBuzz(n) {\n  const result = [];\n  for (let i = 1; i <= n; i++) {\n    if (i % 15 === 0) {\n      result.push('FizzBuzz');\n    } else if (i % 3 === 0) {\n      result.push('Fizz');\n    } else if (i % 5 === 0) {\n      result.push('Buzz');\n    } else {\n      result.push(i);\n    }\n  }\n  return result;\n}",
    testCases: [
      {
        input: [15],
        expected: [1, 2, 'Fizz', 4, 'Buzz', 'Fizz', 7, 8, 'Fizz', '<PERSON>', 11, 'Fizz', 13, 14, 'FizzBuzz'],
        description: "fizzBuzz(15) should return correct sequence"
      }
    ],
    hints: [
      "Check for multiples of 15 first (both 3 and 5)",
      "Use modulo operator (%) to check divisibility",
      "Build the result array step by step"
    ],
    concepts: ["algorithms", "modulo", "conditionals", "classic problems"],
    unlocks: [],
    prerequisite: 12
  },
  {
    id: 14,
    title: "Prime Number Checker",
    description: "Write a function that checks if a number is prime.",
    difficulty: "intermediate",
    category: "algorithms",
    instructions: "Create a function called 'isPrime' that returns true if the number is prime, false otherwise.",
    starterCode: "function isPrime(num) {\n  // Write your code here\n  \n}",
    solution: "function isPrime(num) {\n  if (num < 2) return false;\n  if (num === 2) return true;\n  if (num % 2 === 0) return false;\n  \n  for (let i = 3; i <= Math.sqrt(num); i += 2) {\n    if (num % i === 0) return false;\n  }\n  return true;\n}",
    testCases: [
      {
        input: [7],
        expected: true,
        description: "isPrime(7) should return true"
      },
      {
        input: [8],
        expected: false,
        description: "isPrime(8) should return false"
      },
      {
        input: [2],
        expected: true,
        description: "isPrime(2) should return true"
      }
    ],
    hints: [
      "Numbers less than 2 are not prime",
      "Only check divisors up to the square root",
      "Skip even numbers after checking for 2"
    ],
    concepts: ["algorithms", "mathematics", "optimization", "prime numbers"],
    unlocks: [],
    prerequisite: 13
  },
  {
    id: 15,
    title: "Factorial Calculator",
    description: "Write a function that calculates the factorial of a number.",
    difficulty: "beginner",
    category: "algorithms",
    instructions: "Create a function called 'factorial' that returns n! (n factorial).",
    starterCode: "function factorial(n) {\n  // Write your code here\n  \n}",
    solution: "function factorial(n) {\n  if (n <= 1) return 1;\n  let result = 1;\n  for (let i = 2; i <= n; i++) {\n    result *= i;\n  }\n  return result;\n}",
    testCases: [
      {
        input: [5],
        expected: 120,
        description: "factorial(5) should return 120"
      },
      {
        input: [0],
        expected: 1,
        description: "factorial(0) should return 1"
      },
      {
        input: [3],
        expected: 6,
        description: "factorial(3) should return 6"
      }
    ],
    hints: [
      "Factorial of 0 and 1 is 1",
      "Use a loop to multiply numbers from 2 to n",
      "Or use recursion: n! = n * (n-1)!"
    ],
    concepts: ["algorithms", "mathematics", "recursion", "loops"],
    unlocks: [],
    prerequisite: 14
  }
];

async function getAdditionalTasks() {
  // In the future, this could fetch from an external API
  // For now, return our predefined additional tasks
  return additionalTasks;
}

async function addTasksToDatabase() {
  try {
    // Read current tasks
    const tasksData = await fs.readFile('./data/tasks.json', 'utf8');
    const currentTasks = JSON.parse(tasksData);
    
    // Get additional tasks
    const newTasks = await getAdditionalTasks();
    
    // Check which tasks are not already in the database
    const existingIds = currentTasks.tasks.map(t => t.id);
    const tasksToAdd = newTasks.filter(t => !existingIds.includes(t.id));
    
    if (tasksToAdd.length > 0) {
      // Add new tasks
      currentTasks.tasks.push(...tasksToAdd);
      
      // Write back to file
      await fs.writeFile('./data/tasks.json', JSON.stringify(currentTasks, null, 2));
      
      console.log(`Added ${tasksToAdd.length} new tasks to the database`);
      return tasksToAdd;
    } else {
      console.log('No new tasks to add');
      return [];
    }
  } catch (error) {
    console.error('Error adding tasks to database:', error);
    return [];
  }
}

// Future: Integration with external APIs
async function fetchTasksFromAPI(difficulty = 'beginner', category = 'general') {
  // This could integrate with services like:
  // - LeetCode API
  // - HackerRank API
  // - Custom coding challenge APIs
  // - AI-generated tasks from OpenAI/Claude
  
  // Example API integration structure:
  /*
  try {
    const response = await fetch(`https://api.codingchallenges.com/tasks?difficulty=${difficulty}&category=${category}`);
    const data = await response.json();
    return data.tasks.map(task => ({
      id: task.id,
      title: task.title,
      description: task.description,
      difficulty: task.difficulty,
      category: task.category,
      instructions: task.instructions,
      starterCode: task.starterCode,
      solution: task.solution,
      testCases: task.testCases,
      hints: task.hints,
      concepts: task.concepts,
      unlocks: [],
      prerequisite: null
    }));
  } catch (error) {
    console.error('Error fetching tasks from API:', error);
    return [];
  }
  */
  
  return [];
}

module.exports = {
  getAdditionalTasks,
  addTasksToDatabase,
  fetchTasksFromAPI
};
