// Global state
let currentTask = null;
let currentProject = null;
let currentType = "task"; // 'task' or 'project'
let progress = null;
let tasks = [];
let projects = [];

// DOM elements
const taskContent = document.getElementById("task-content");
const codeEditor = document.getElementById("code-editor");
const runCodeBtn = document.getElementById("run-code");
const resetCodeBtn = document.getElementById("reset-code");
const getHintBtn = document.getElementById("get-hint");
const resultsSection = document.getElementById("results-section");
const resultsContent = document.getElementById("results-content");
const feedbackSection = document.getElementById("feedback-section");
const feedbackContent = document.getElementById("feedback-content");
const currentTaskNum = document.getElementById("current-task-num");
const totalTasksSpan = document.getElementById("total-tasks");
const currentTaskType = document.getElementById("current-task-type");
const achievementFill = document.getElementById("achievement-fill");
const achievementText = document.getElementById("achievement-text");
const prevTaskBtn = document.getElementById("prev-task");
const nextTaskBtn = document.getElementById("next-task");
const progressText = document.getElementById("progress-text");
const progressStats = document.getElementById("progress-stats");
const progressFill = document.getElementById("progress-fill");
const successModal = document.getElementById("success-modal");
const hintModal = document.getElementById("hint-modal");

// Initialize the application
document.addEventListener("DOMContentLoaded", async () => {
  initializeTheme();
  await loadInitialData();
  setupEventListeners();
  await loadCurrentTask();
});

// Load initial data
async function loadInitialData() {
  try {
    // Load progress
    const progressResponse = await fetch("/api/progress");
    progress = await progressResponse.json();

    // Load tasks
    const tasksResponse = await fetch("/api/tasks");
    const tasksData = await tasksResponse.json();
    tasks = tasksData.tasks;

    // Load projects
    const projectsResponse = await fetch("/api/projects");
    const projectsData = await projectsResponse.json();
    projects = projectsData.projects;

    updateProgressDisplay();
    updateTaskNavigation();
  } catch (error) {
    console.error("Error loading initial data:", error);
    showError("Failed to load application data. Please refresh the page.");
  }
}

// Theme Management
function initializeTheme() {
  const savedTheme = localStorage.getItem("theme") || "light";
  document.documentElement.setAttribute("data-theme", savedTheme);
  updateThemeToggle(savedTheme);
}

function toggleTheme() {
  const currentTheme = document.documentElement.getAttribute("data-theme");
  const newTheme = currentTheme === "dark" ? "light" : "dark";

  document.documentElement.setAttribute("data-theme", newTheme);
  localStorage.setItem("theme", newTheme);
  updateThemeToggle(newTheme);

  // Add smooth transition effect
  document.body.style.transition = "all 0.3s ease";
  setTimeout(() => {
    document.body.style.transition = "";
  }, 300);
}

function updateThemeToggle(theme) {
  const themeToggle = document.getElementById("theme-toggle");
  const themeIcon = themeToggle.querySelector(".theme-icon");
  const themeText = themeToggle.querySelector(".theme-text");

  if (theme === "dark") {
    themeIcon.textContent = "☀️";
    themeText.textContent = "Light";
  } else {
    themeIcon.textContent = "🌙";
    themeText.textContent = "Dark";
  }
}

// Setup event listeners
function setupEventListeners() {
  runCodeBtn.addEventListener("click", runCode);
  resetCodeBtn.addEventListener("click", resetCode);
  getHintBtn.addEventListener("click", showHint);

  // Format button
  document.getElementById("format-code").addEventListener("click", formatCode);

  // Theme toggle
  document
    .getElementById("theme-toggle")
    .addEventListener("click", toggleTheme);

  // Navigation buttons
  prevTaskBtn.addEventListener("click", navigateToPrevious);
  nextTaskBtn.addEventListener("click", navigateToNext);

  // Code editor enhancements
  setupCodeEditor();

  document.getElementById("continue-btn").addEventListener("click", () => {
    successModal.style.display = "none";
    loadCurrentTask();
  });
}

// Code Editor Setup with Tab Support and Formatting
function setupCodeEditor() {
  // Tab key support
  codeEditor.addEventListener("keydown", function (e) {
    if (e.key === "Tab") {
      e.preventDefault();

      const start = this.selectionStart;
      const end = this.selectionEnd;
      const value = this.value;

      if (e.shiftKey) {
        // Shift+Tab: Remove indentation
        const lineStart = value.lastIndexOf("\n", start - 1) + 1;
        const lineEnd = value.indexOf("\n", end);
        const actualEnd = lineEnd === -1 ? value.length : lineEnd;

        const lines = value.substring(lineStart, actualEnd).split("\n");
        const newLines = lines.map((line) => {
          if (line.startsWith("  ")) {
            return line.substring(2);
          } else if (line.startsWith("\t")) {
            return line.substring(1);
          }
          return line;
        });

        const newText = newLines.join("\n");
        const lengthDiff = actualEnd - lineStart - newText.length;

        this.value =
          value.substring(0, lineStart) + newText + value.substring(actualEnd);
        this.selectionStart = Math.max(lineStart, start - lengthDiff);
        this.selectionEnd = Math.max(lineStart, end - lengthDiff);
      } else {
        // Tab: Add indentation
        if (start === end) {
          // Single cursor - insert tab
          this.value = value.substring(0, start) + "  " + value.substring(end);
          this.selectionStart = this.selectionEnd = start + 2;
        } else {
          // Selection - indent all lines
          const lineStart = value.lastIndexOf("\n", start - 1) + 1;
          const lineEnd = value.indexOf("\n", end);
          const actualEnd = lineEnd === -1 ? value.length : lineEnd;

          const lines = value.substring(lineStart, actualEnd).split("\n");
          const newLines = lines.map((line) => "  " + line);
          const newText = newLines.join("\n");

          this.value =
            value.substring(0, lineStart) +
            newText +
            value.substring(actualEnd);
          this.selectionStart = start + 2;
          this.selectionEnd = end + newLines.length * 2;
        }
      }
    }

    // Auto-close brackets and quotes
    if (e.key === "(" && !e.ctrlKey && !e.metaKey) {
      e.preventDefault();
      insertTextWithPair("(", ")");
    } else if (e.key === "{" && !e.ctrlKey && !e.metaKey) {
      e.preventDefault();
      insertTextWithPair("{", "}");
    } else if (e.key === "[" && !e.ctrlKey && !e.metaKey) {
      e.preventDefault();
      insertTextWithPair("[", "]");
    } else if (e.key === '"' && !e.ctrlKey && !e.metaKey) {
      e.preventDefault();
      insertTextWithPair('"', '"');
    } else if (e.key === "'" && !e.ctrlKey && !e.metaKey) {
      e.preventDefault();
      insertTextWithPair("'", "'");
    }

    // Format code with Ctrl+Shift+F
    if (e.ctrlKey && e.shiftKey && e.key === "F") {
      e.preventDefault();
      formatCode();
    }
  });

  // Auto-indent on Enter
  codeEditor.addEventListener("keypress", function (e) {
    if (e.key === "Enter") {
      const start = this.selectionStart;
      const value = this.value;
      const lineStart = value.lastIndexOf("\n", start - 1) + 1;
      const currentLine = value.substring(lineStart, start);
      const indent = currentLine.match(/^(\s*)/)[1];

      // Add extra indent for opening braces
      const extraIndent = currentLine.trim().endsWith("{") ? "  " : "";

      setTimeout(() => {
        const newStart = this.selectionStart;
        this.value =
          this.value.substring(0, newStart) +
          indent +
          extraIndent +
          this.value.substring(newStart);
        this.selectionStart = this.selectionEnd =
          newStart + indent.length + extraIndent.length;
      }, 0);
    }
  });
}

function insertTextWithPair(open, close) {
  const start = codeEditor.selectionStart;
  const end = codeEditor.selectionEnd;
  const value = codeEditor.value;
  const selectedText = value.substring(start, end);

  codeEditor.value =
    value.substring(0, start) +
    open +
    selectedText +
    close +
    value.substring(end);
  codeEditor.selectionStart = codeEditor.selectionEnd =
    start + open.length + selectedText.length;

  if (selectedText === "") {
    codeEditor.selectionStart = start + open.length;
  }
}

// Simple JavaScript formatter
function formatCode() {
  const code = codeEditor.value;
  const formatted = beautifyJavaScript(code);
  codeEditor.value = formatted;

  // Show formatting notification
  showNotification("Code formatted! 🎨", "success");
}

function beautifyJavaScript(code) {
  try {
    let formatted = code;
    let indentLevel = 0;
    const lines = formatted.split("\n");
    const result = [];

    for (let line of lines) {
      const trimmed = line.trim();
      if (trimmed === "") {
        result.push("");
        continue;
      }

      // Decrease indent for closing braces
      if (trimmed.startsWith("}")) {
        indentLevel = Math.max(0, indentLevel - 1);
      }

      // Add current line with proper indentation
      const indent = "  ".repeat(indentLevel);
      result.push(indent + trimmed);

      // Increase indent for opening braces
      if (trimmed.endsWith("{")) {
        indentLevel++;
      }
    }

    return result.join("\n");
  } catch (error) {
    console.error("Formatting error:", error);
    return code; // Return original code if formatting fails
  }
}

// Update progress display
function updateProgressDisplay() {
  if (!progress) return;

  const totalTasks = tasks.length;
  const completedTasks = progress.completedTasks.length;
  const progressPercentage =
    totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

  progressText.textContent = `Progress: ${completedTasks}/${totalTasks} tasks completed`;
  progressStats.textContent = `Projects: ${progress.completedProjects.length}`;
  progressFill.style.width = `${progressPercentage}%`;
}

// Update task navigation
function updateTaskNavigation() {
  if (!progress || !tasks.length) return;

  // Update total tasks count
  totalTasksSpan.textContent = tasks.length;

  // Find current task index
  const currentTaskIndex = tasks.findIndex(
    (t) => t.id === (currentTask?.id || progress.currentTask)
  );
  const taskNumber = currentTaskIndex >= 0 ? currentTaskIndex + 1 : 1;

  // Update current task number
  currentTaskNum.textContent = taskNumber;

  // Update task type based on current task
  if (currentTask) {
    currentTaskType.textContent = getCategoryDisplayName(currentTask.category);
  } else if (currentProject) {
    currentTaskType.textContent = "Mini Project";
  }

  // Update navigation buttons
  prevTaskBtn.disabled = taskNumber <= 1;
  nextTaskBtn.disabled = taskNumber >= tasks.length;

  // Update achievement bar
  const completedTasks = progress.completedTasks.length;
  const achievementPercentage = (completedTasks / tasks.length) * 100;
  achievementFill.style.width = `${achievementPercentage}%`;

  // Update achievement text
  const achievements = [
    { threshold: 0, text: "🎯 Start Your Journey" },
    { threshold: 3, text: "🔥 Building Momentum" },
    { threshold: 6, text: "⚡ Getting Stronger" },
    { threshold: 9, text: "🚀 Almost There" },
    { threshold: 12, text: "🏆 JavaScript Master" },
  ];

  const currentAchievement = achievements
    .reverse()
    .find((a) => completedTasks >= a.threshold);
  achievementText.textContent =
    currentAchievement?.text || "🎯 Start Your Journey";
}

function getCategoryDisplayName(category) {
  const categoryMap = {
    "basic-operations": "Basic Math",
    conditionals: "Logic & Decisions",
    loops: "Loops & Arrays",
    strings: "Text Processing",
    functions: "Functions",
    objects: "Objects & Data",
  };
  return categoryMap[category] || "Programming";
}

// Navigation functions
function navigateToPrevious() {
  const currentIndex = tasks.findIndex(
    (t) => t.id === (currentTask?.id || progress.currentTask)
  );
  if (currentIndex > 0) {
    const prevTask = tasks[currentIndex - 1];
    if (progress.unlockedTasks.includes(prevTask.id)) {
      loadTask(prevTask.id);
    }
  }
}

function navigateToNext() {
  const currentIndex = tasks.findIndex(
    (t) => t.id === (currentTask?.id || progress.currentTask)
  );
  if (currentIndex < tasks.length - 1) {
    const nextTask = tasks[currentIndex + 1];
    if (progress.unlockedTasks.includes(nextTask.id)) {
      loadTask(nextTask.id);
    }
  }
}

// Load current task
async function loadCurrentTask() {
  const currentTaskId = progress.currentTask;
  const task = tasks.find((t) => t.id === currentTaskId);

  if (
    task &&
    progress.unlockedTasks.includes(currentTaskId) &&
    !progress.completedTasks.includes(currentTaskId)
  ) {
    await loadTask(currentTaskId);
  } else {
    // Find first available task
    const availableTask = tasks.find(
      (t) =>
        progress.unlockedTasks.includes(t.id) &&
        !progress.completedTasks.includes(t.id)
    );

    if (availableTask) {
      await loadTask(availableTask.id);
    } else {
      showCompletionMessage();
    }
  }
}

// Load specific task
async function loadTask(taskId) {
  const task = tasks.find((t) => t.id === taskId);
  if (!task) return;

  currentTask = task;
  currentProject = null;
  currentType = "task";

  displayTaskContent(task);
  codeEditor.value = task.starterCode;
  hideResults();
  hideFeedback();
  updateTaskNavigation();
}

// Load specific project
async function loadProject(projectId) {
  const project = projects.find((p) => p.id === projectId);
  if (!project) return;

  currentProject = project;
  currentTask = null;
  currentType = "project";

  displayProjectContent(project);
  codeEditor.value = project.starterCode;
  hideResults();
  hideFeedback();
  updateTaskNavigation();
}

// Display task content
function displayTaskContent(task) {
  taskContent.innerHTML = `
        <h2>${task.title}</h2>
        <p class="description">${task.description}</p>
        <div class="instructions">
            <strong>Instructions:</strong><br>
            ${task.instructions}
        </div>
        <div class="concepts">
            <strong>Concepts:</strong>
            ${task.concepts
              .map((concept) => `<span class="concept-tag">${concept}</span>`)
              .join("")}
        </div>
    `;
}

// Display project content
function displayProjectContent(project) {
  taskContent.innerHTML = `
        <h2>🎯 ${project.title}</h2>
        <p class="description">${project.description}</p>
        <div class="instructions">
            <strong>Instructions:</strong><br>
            ${project.instructions}
        </div>
        <div class="concepts">
            <strong>Concepts:</strong>
            ${project.concepts
              .map((concept) => `<span class="concept-tag">${concept}</span>`)
              .join("")}
        </div>
    `;
}

// Run code
async function runCode() {
  const code = codeEditor.value.trim();
  if (!code) {
    showError("Please write some code first!");
    return;
  }

  runCodeBtn.disabled = true;
  runCodeBtn.textContent = "⏳ Running...";

  try {
    const response = await fetch("/api/execute", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({
        code: code,
        taskId: currentType === "task" ? currentTask.id : currentProject.id,
        type: currentType,
      }),
    });

    const result = await response.json();
    displayResults(result);

    if (result.success) {
      await handleSuccess();
    } else if (result.feedback) {
      displayFeedback(result.feedback);
    }
  } catch (error) {
    console.error("Error running code:", error);
    showError("Failed to run code. Please try again.");
  } finally {
    runCodeBtn.disabled = false;
    runCodeBtn.textContent = "▶️ Run Code";
  }
}

// Display results
function displayResults(result) {
  resultsContent.innerHTML = "";

  if (result.output && result.output.length > 0) {
    const outputDiv = document.createElement("div");
    outputDiv.innerHTML = `
            <h4>📄 Console Output:</h4>
            <pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; margin: 10px 0;">${result.output.join(
              "\n"
            )}</pre>
        `;
    resultsContent.appendChild(outputDiv);
  }

  if (result.testResults && result.testResults.length > 0) {
    const testsDiv = document.createElement("div");
    testsDiv.innerHTML = "<h4>🧪 Test Results:</h4>";

    result.testResults.forEach((test) => {
      const testDiv = document.createElement("div");
      testDiv.className = `test-result ${test.passed ? "passed" : "failed"}`;
      testDiv.innerHTML = `
                <div class="test-description">${test.description}</div>
                <div class="test-details">
                    Expected: ${test.expected}<br>
                    Got: ${test.actual}
                    ${test.error ? `<br>Error: ${test.error}` : ""}
                </div>
            `;
      testsDiv.appendChild(testDiv);
    });

    resultsContent.appendChild(testsDiv);
  }

  if (result.message) {
    const messageDiv = document.createElement("div");
    messageDiv.className = result.success ? "success-message" : "error-message";
    messageDiv.style.cssText = `
            padding: 15px;
            margin: 15px 0;
            border-radius: 8px;
            font-weight: bold;
            ${
              result.success
                ? "background: #e8f5e8; color: #2e7d32; border-left: 4px solid #4caf50;"
                : "background: #ffebee; color: #c62828; border-left: 4px solid #f44336;"
            }
        `;
    messageDiv.textContent = result.message;
    resultsContent.appendChild(messageDiv);
  }

  resultsSection.style.display = "block";
}

// Display feedback
function displayFeedback(feedback) {
  feedbackContent.innerHTML = `
        <div class="feedback-title">${feedback.title}</div>
        <div class="feedback-explanation">${feedback.explanation}</div>
        ${
          feedback.tips && feedback.tips.length > 0
            ? `
            <div class="feedback-tips">
                <strong>💡 Tips:</strong>
                <ul>
                    ${feedback.tips.map((tip) => `<li>${tip}</li>`).join("")}
                </ul>
            </div>
        `
            : ""
        }
        ${
          feedback.encouragement
            ? `
            <div class="encouragement">${feedback.encouragement}</div>
        `
            : ""
        }
    `;

  feedbackSection.style.display = "block";
}

// Handle successful completion
async function handleSuccess() {
  try {
    // Update progress on server
    let updateData;
    if (currentType === "task") {
      // Check if task is already completed
      if (progress.completedTasks.includes(currentTask.id)) {
        console.log("Task already completed");
        return;
      }

      const newCompletedTasks = [...progress.completedTasks, currentTask.id];
      const newUnlockedTasks = [...progress.unlockedTasks];

      // Unlock next task
      const nextTaskId = currentTask.id + 1;
      const nextTask = tasks.find((t) => t.id === nextTaskId);
      if (nextTask && !newUnlockedTasks.includes(nextTaskId)) {
        newUnlockedTasks.push(nextTaskId);
      }

      // Check for unlocked projects
      const newUnlockedProjects = [...progress.unlockedProjects];
      if (currentTask.unlocks) {
        for (const unlockId of currentTask.unlocks) {
          if (!newUnlockedProjects.includes(unlockId)) {
            const project = projects.find((p) => p.id === unlockId);
            if (project) {
              // Check if all prerequisite tasks are completed
              const allPrereqsCompleted = project.prerequisiteTasks.every(
                (prereqId) => newCompletedTasks.includes(prereqId)
              );
              if (allPrereqsCompleted) {
                newUnlockedProjects.push(unlockId);
              }
            }
          }
        }
      }

      updateData = {
        completedTasks: newCompletedTasks,
        unlockedTasks: newUnlockedTasks,
        unlockedProjects: newUnlockedProjects,
        currentTask: nextTask ? nextTaskId : progress.currentTask,
        stats: {
          ...progress.stats,
          totalTasksCompleted: progress.stats.totalTasksCompleted + 1,
          lastCompletionDate: new Date().toISOString(),
        },
        user: {
          ...progress.user,
          lastActivity: new Date().toISOString(),
        },
      };
    } else {
      updateData = {
        completedProjects: [...progress.completedProjects, currentProject.id],
        stats: {
          ...progress.stats,
          totalProjectsCompleted: progress.stats.totalProjectsCompleted + 1,
          lastCompletionDate: new Date().toISOString(),
        },
        user: {
          ...progress.user,
          lastActivity: new Date().toISOString(),
        },
      };
    }

    const response = await fetch("/api/progress", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(updateData),
    });

    progress = await response.json();
    updateProgressDisplay();
    updateTaskNavigation();

    showSuccessModal();
  } catch (error) {
    console.error("Error updating progress:", error);
  }
}

// Show success modal
function showSuccessModal() {
  const successMessage = document.getElementById("success-message");
  const unlockMessage = document.getElementById("unlock-message");
  const unlockedContent = document.getElementById("unlocked-content");

  if (currentType === "task") {
    successMessage.textContent = `Excellent work! You've completed "${currentTask.title}"!`;

    // Check for unlocked content
    const unlockedProjects = projects.filter(
      (p) =>
        progress.unlockedProjects.includes(p.id) &&
        !progress.completedProjects.includes(p.id)
    );

    if (unlockedProjects.length > 0) {
      unlockMessage.style.display = "block";
      unlockedContent.innerHTML = unlockedProjects
        .map(
          (p) =>
            `<div style="padding: 10px; background: #fff3e0; margin: 5px 0; border-radius: 6px;">
                    🎯 ${p.title}
                </div>`
        )
        .join("");
    } else {
      unlockMessage.style.display = "none";
    }
  } else {
    successMessage.textContent = `Amazing! You've completed the "${currentProject.title}" project!`;
    unlockMessage.style.display = "none";
  }

  successModal.style.display = "flex";
}

// Reset code
function resetCode() {
  if (currentType === "task" && currentTask) {
    codeEditor.value = currentTask.starterCode;
  } else if (currentType === "project" && currentProject) {
    codeEditor.value = currentProject.starterCode;
  }
  hideResults();
  hideFeedback();
}

// Show hint
function showHint() {
  const hintContent = document.getElementById("hint-content");
  let hints = [];

  if (currentType === "task" && currentTask) {
    hints = currentTask.hints || [];
  } else if (currentType === "project" && currentProject) {
    hints = currentProject.hints || [];
  }

  if (hints.length > 0) {
    hintContent.innerHTML = `
            <div style="margin-bottom: 15px;">
                <strong>Here are some hints to help you:</strong>
            </div>
            <ul style="margin-left: 20px;">
                ${hints
                  .map((hint) => `<li style="margin-bottom: 8px;">${hint}</li>`)
                  .join("")}
            </ul>
        `;
  } else {
    hintContent.innerHTML =
      "<p>No hints available for this task. You've got this! 💪</p>";
  }

  hintModal.style.display = "flex";
}

// Close hint modal
function closeHintModal() {
  hintModal.style.display = "none";
}

// Utility functions
function hideResults() {
  resultsSection.style.display = "none";
}

function hideFeedback() {
  feedbackSection.style.display = "none";
}

function showError(message) {
  showNotification(message, "error");
}

function showNotification(message, type = "info") {
  // Create notification element
  const notification = document.createElement("div");
  notification.className = `notification notification-${type}`;
  notification.textContent = message;

  // Add to page
  document.body.appendChild(notification);

  // Animate in
  setTimeout(() => notification.classList.add("show"), 100);

  // Remove after 3 seconds
  setTimeout(() => {
    notification.classList.remove("show");
    setTimeout(() => document.body.removeChild(notification), 300);
  }, 3000);
}

function showCompletionMessage() {
  taskContent.innerHTML = `
        <div style="text-align: center; padding: 60px 20px;">
            <h2>🎉 Congratulations!</h2>
            <p style="font-size: 1.2rem; margin: 20px 0; color: #666;">
                You've completed all available tasks! 
            </p>
            <p style="color: #666;">
                More content coming soon. Keep practicing and exploring JavaScript!
            </p>
        </div>
    `;
}

// Close modals when clicking outside
window.addEventListener("click", (e) => {
  if (e.target === successModal) {
    successModal.style.display = "none";
  }
  if (e.target === hintModal) {
    hintModal.style.display = "none";
  }
});
