/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  color: #333;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 15px;
}

/* Header */
.header {
  text-align: center;
  background: rgba(255, 255, 255, 0.95);
  padding: 15px 20px;
  border-radius: 10px;
  margin-bottom: 15px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

.header h1 {
  font-size: 1.8rem;
  margin-bottom: 5px;
  background: linear-gradient(45deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header p {
  font-size: 0.9rem;
  color: #666;
  margin: 0;
}

/* Progress Section */
.progress-section {
  background: rgba(255, 255, 255, 0.95);
  padding: 12px 15px;
  border-radius: 8px;
  margin-bottom: 15px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4caf50, #45a049);
  border-radius: 4px;
  transition: width 0.3s ease;
  width: 0%;
}

/* Main Content Layout */
.main-content {
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: 15px;
  min-height: 500px;
}

/* Sidebar */
.sidebar {
  background: rgba(255, 255, 255, 0.95);
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: fit-content;
}

.nav-section {
  margin-bottom: 20px;
}

.nav-section h3 {
  margin-bottom: 10px;
  color: #333;
  font-size: 1rem;
}

.task-list,
.project-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.task-item,
.project-item {
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
  font-size: 0.9rem;
}

.task-item.completed {
  background: #e8f5e8;
  color: #2e7d32;
}

.task-item.available {
  background: #e3f2fd;
  color: #1976d2;
}

.task-item.locked {
  background: #f5f5f5;
  color: #999;
  cursor: not-allowed;
}

.task-item.current {
  border-color: #667eea;
  background: #f0f4ff;
  font-weight: bold;
}

.task-item:hover:not(.locked) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.project-item.available {
  background: #fff3e0;
  color: #f57c00;
}

.project-item.completed {
  background: #e8f5e8;
  color: #2e7d32;
}

.project-item.locked {
  background: #f5f5f5;
  color: #999;
  cursor: not-allowed;
}

/* Content Area */
.content {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 10px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.task-content {
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.task-content h2 {
  color: #333;
  margin-bottom: 10px;
  font-size: 1.5rem;
}

.task-content .description {
  color: #666;
  margin-bottom: 15px;
  font-size: 1rem;
  line-height: 1.5;
}

.task-content .instructions {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 15px;
  border-left: 4px solid #667eea;
  font-size: 0.95rem;
}

.task-content .concepts {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 15px;
}

.concept-tag {
  background: #e3f2fd;
  color: #1976d2;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

/* Editor Section */
.editor-section {
  padding: 15px 20px;
  border-bottom: 1px solid #eee;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.editor-controls {
  display: flex;
  gap: 8px;
}

.code-editor {
  width: 100%;
  height: 250px;
  font-family: "Consolas", "Monaco", "Courier New", monospace;
  font-size: 13px;
  padding: 12px;
  border: 2px solid #e0e0e0;
  border-radius: 6px;
  background: #1e1e1e;
  color: #d4d4d4;
  resize: vertical;
  line-height: 1.4;
}

.code-editor:focus {
  outline: none;
  border-color: #667eea;
}

/* Buttons */
.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.btn-primary {
  background: #667eea;
  color: white;
}

.btn-primary:hover {
  background: #5a6fd8;
  transform: translateY(-1px);
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background: #5a6268;
}

.btn-hint {
  background: #ffc107;
  color: #333;
}

.btn-hint:hover {
  background: #e0a800;
}

/* Results Section */
.results-section,
.feedback-section {
  padding: 15px 20px;
}

.results-content,
.feedback-content {
  margin-top: 10px;
}

.test-result {
  padding: 10px 12px;
  margin-bottom: 8px;
  border-radius: 6px;
  border-left: 4px solid;
}

.test-result.passed {
  background: #e8f5e8;
  border-color: #4caf50;
  color: #2e7d32;
}

.test-result.failed {
  background: #ffebee;
  border-color: #f44336;
  color: #c62828;
}

.test-description {
  font-weight: bold;
  margin-bottom: 5px;
}

.test-details {
  font-size: 0.9rem;
  opacity: 0.8;
}

/* Feedback Styling */
.feedback-content {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #ff9800;
}

.feedback-title {
  color: #e65100;
  font-weight: bold;
  margin-bottom: 10px;
}

.feedback-explanation {
  margin-bottom: 15px;
  line-height: 1.6;
}

.feedback-tips {
  background: white;
  padding: 15px;
  border-radius: 6px;
  margin-top: 15px;
}

.feedback-tips ul {
  margin-left: 20px;
}

.feedback-tips li {
  margin-bottom: 5px;
}

.encouragement {
  background: #e8f5e8;
  color: #2e7d32;
  padding: 10px;
  border-radius: 6px;
  margin-top: 15px;
  text-align: center;
  font-weight: 500;
}

/* Modal Styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  padding: 0;
  border-radius: 12px;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
  padding: 20px 30px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-body {
  padding: 30px;
}

.modal-footer {
  padding: 20px 30px;
  border-top: 1px solid #eee;
  text-align: right;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #999;
}

.unlock-message {
  background: #e8f5e8;
  padding: 20px;
  border-radius: 8px;
  margin-top: 20px;
}

.unlock-message h4 {
  color: #2e7d32;
  margin-bottom: 10px;
}

/* Loading State */
.loading {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

/* Responsive Design */
@media (max-width: 768px) {
  .main-content {
    grid-template-columns: 1fr;
  }

  .sidebar {
    order: 2;
  }

  .content {
    order: 1;
  }

  .editor-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .editor-controls {
    justify-content: center;
  }
}
