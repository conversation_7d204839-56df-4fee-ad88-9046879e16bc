/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* Light Mode Colors */
  --bg-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --bg-secondary: rgba(255, 255, 255, 0.95);
  --bg-tertiary: #f8f9fa;
  --text-primary: #333;
  --text-secondary: #666;
  --text-muted: #999;
  --border-color: #e0e0e0;
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.1);
  --accent-primary: #667eea;
  --accent-secondary: #764ba2;
  --success-bg: #e8f5e8;
  --success-color: #2e7d32;
  --error-bg: #ffebee;
  --error-color: #c62828;
  --warning-bg: #fff3e0;
  --warning-color: #f57c00;
  --editor-bg: #1e1e1e;
  --editor-text: #d4d4d4;
}

[data-theme="dark"] {
  /* Dark Mode Colors */
  --bg-primary: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  --bg-secondary: rgba(30, 30, 30, 0.95);
  --bg-tertiary: #2a2a2a;
  --text-primary: #e0e0e0;
  --text-secondary: #b0b0b0;
  --text-muted: #888;
  --border-color: #404040;
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.3);
  --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.3);
  --accent-primary: #7c8aff;
  --accent-secondary: #8b5cf6;
  --success-bg: #1a3d1a;
  --success-color: #4caf50;
  --error-bg: #3d1a1a;
  --error-color: #f44336;
  --warning-bg: #3d2a1a;
  --warning-color: #ff9800;
  --editor-bg: #0d1117;
  --editor-text: #c9d1d9;
}

body {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  background: var(--bg-primary);
  min-height: 100vh;
  color: var(--text-primary);
  transition: all 0.3s ease;
}

.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 15px;
}

/* Header */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--bg-secondary);
  padding: 15px 20px;
  border-radius: 12px;
  margin-bottom: 15px;
  box-shadow: var(--shadow-medium);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.header-content {
  text-align: center;
  flex: 1;
}

.header h1 {
  font-size: 1.8rem;
  margin-bottom: 5px;
  background: linear-gradient(
    45deg,
    var(--accent-primary),
    var(--accent-secondary)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from {
    filter: brightness(1);
  }
  to {
    filter: brightness(1.2);
  }
}

.header p {
  font-size: 0.9rem;
  color: var(--text-secondary);
  margin: 0;
}

.theme-toggle {
  background: var(--bg-tertiary);
  border: 2px solid var(--border-color);
  border-radius: 25px;
  padding: 8px 16px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  color: var(--text-primary);
}

.theme-toggle:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-light);
  border-color: var(--accent-primary);
}

.theme-icon {
  font-size: 1.2rem;
  transition: transform 0.3s ease;
}

.theme-toggle:hover .theme-icon {
  transform: rotate(180deg);
}

/* Progress Section */
.progress-section {
  background: var(--bg-secondary);
  padding: 12px 15px;
  border-radius: 10px;
  margin-bottom: 15px;
  box-shadow: var(--shadow-light);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 0.9rem;
  color: var(--text-primary);
}

.progress-bar {
  width: 100%;
  height: 10px;
  background: var(--border-color);
  border-radius: 5px;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(
    90deg,
    var(--success-color),
    var(--accent-primary)
  );
  border-radius: 5px;
  transition: width 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  width: 0%;
  position: relative;
}

.progress-fill::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Main Content Layout */
.main-content {
  display: grid;
  grid-template-columns: 280px 1fr;
  gap: 15px;
  min-height: 500px;
}

/* Sidebar */
.sidebar {
  background: var(--bg-secondary);
  padding: 15px;
  border-radius: 12px;
  box-shadow: var(--shadow-light);
  backdrop-filter: blur(10px);
  height: fit-content;
  transition: all 0.3s ease;
}

.nav-section {
  margin-bottom: 20px;
}

.nav-section h3 {
  margin-bottom: 10px;
  color: var(--text-primary);
  font-size: 1rem;
  font-weight: 600;
}

.task-list,
.project-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.task-item,
.project-item {
  padding: 12px;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid transparent;
  font-size: 0.9rem;
  position: relative;
  overflow: hidden;
}

.task-item::before,
.project-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transition: left 0.5s;
}

.task-item:hover::before,
.project-item:hover::before {
  left: 100%;
}

.task-item.completed {
  background: var(--success-bg);
  color: var(--success-color);
  border-color: var(--success-color);
}

.task-item.available {
  background: var(--bg-tertiary);
  color: var(--accent-primary);
  border-color: transparent;
}

.task-item.locked {
  background: var(--border-color);
  color: var(--text-muted);
  cursor: not-allowed;
  opacity: 0.6;
}

.task-item.current {
  border-color: var(--accent-primary);
  background: var(--bg-tertiary);
  font-weight: bold;
  box-shadow: 0 0 20px rgba(102, 126, 234, 0.3);
}

.task-item:hover:not(.locked) {
  transform: translateY(-3px) scale(1.02);
  box-shadow: var(--shadow-medium);
  border-color: var(--accent-primary);
}

.project-item.available {
  background: var(--warning-bg);
  color: var(--warning-color);
  border-color: transparent;
}

.project-item.completed {
  background: var(--success-bg);
  color: var(--success-color);
  border-color: var(--success-color);
}

.project-item.locked {
  background: var(--border-color);
  color: var(--text-muted);
  cursor: not-allowed;
  opacity: 0.6;
}

/* Content Area */
.content {
  background: var(--bg-secondary);
  border-radius: 12px;
  box-shadow: var(--shadow-medium);
  backdrop-filter: blur(10px);
  overflow: hidden;
  transition: all 0.3s ease;
}

.task-content {
  padding: 20px;
  border-bottom: 1px solid var(--border-color);
}

.task-content h2 {
  color: var(--text-primary);
  margin-bottom: 10px;
  font-size: 1.5rem;
  font-weight: 700;
  background: linear-gradient(
    45deg,
    var(--accent-primary),
    var(--accent-secondary)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.task-content .description {
  color: var(--text-secondary);
  margin-bottom: 15px;
  font-size: 1rem;
  line-height: 1.6;
}

.task-content .instructions {
  background: var(--bg-tertiary);
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 15px;
  border-left: 4px solid var(--accent-primary);
  font-size: 0.95rem;
  color: var(--text-primary);
  position: relative;
  overflow: hidden;
}

.task-content .instructions::before {
  content: "💡";
  position: absolute;
  top: 15px;
  right: 15px;
  font-size: 1.2rem;
  opacity: 0.7;
}

.task-content .concepts {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 15px;
}

.concept-tag {
  background: var(--accent-primary);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: default;
}

.concept-tag:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-light);
}

/* Editor Section */
.editor-section {
  padding: 15px 20px;
  border-bottom: 1px solid var(--border-color);
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.editor-header h3 {
  color: var(--text-primary);
  font-size: 1.1rem;
  font-weight: 600;
}

.editor-controls {
  display: flex;
  gap: 8px;
}

.code-editor {
  width: 100%;
  height: 250px;
  font-family: "Fira Code", "Consolas", "Monaco", "Courier New", monospace;
  font-size: 13px;
  padding: 15px;
  border: 2px solid var(--border-color);
  border-radius: 8px;
  background: var(--editor-bg);
  color: var(--editor-text);
  resize: vertical;
  line-height: 1.5;
  transition: all 0.3s ease;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.code-editor:focus {
  outline: none;
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1),
    inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Buttons */
.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 0.9rem;
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: linear-gradient(
    45deg,
    var(--accent-primary),
    var(--accent-secondary)
  );
  color: white;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
  background: linear-gradient(45deg, #6c757d, #5a6268);
  color: white;
  box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

.btn-secondary:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 25px rgba(108, 117, 125, 0.4);
}

.btn-hint {
  background: linear-gradient(45deg, #ffc107, #e0a800);
  color: #333;
  box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
}

.btn-hint:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 25px rgba(255, 193, 7, 0.4);
}

.btn-format {
  background: linear-gradient(45deg, #9c27b0, #673ab7);
  color: white;
  box-shadow: 0 4px 15px rgba(156, 39, 176, 0.3);
}

.btn-format:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 8px 25px rgba(156, 39, 176, 0.4);
}

/* Results Section */
.results-section,
.feedback-section {
  padding: 15px 20px;
}

.results-content,
.feedback-content {
  margin-top: 10px;
}

.test-result {
  padding: 10px 12px;
  margin-bottom: 8px;
  border-radius: 6px;
  border-left: 4px solid;
}

.test-result.passed {
  background: #e8f5e8;
  border-color: #4caf50;
  color: #2e7d32;
}

.test-result.failed {
  background: #ffebee;
  border-color: #f44336;
  color: #c62828;
}

.test-description {
  font-weight: bold;
  margin-bottom: 5px;
}

.test-details {
  font-size: 0.9rem;
  opacity: 0.8;
}

/* Feedback Styling */
.feedback-content {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #ff9800;
}

.feedback-title {
  color: #e65100;
  font-weight: bold;
  margin-bottom: 10px;
}

.feedback-explanation {
  margin-bottom: 15px;
  line-height: 1.6;
}

.feedback-tips {
  background: white;
  padding: 15px;
  border-radius: 6px;
  margin-top: 15px;
}

.feedback-tips ul {
  margin-left: 20px;
}

.feedback-tips li {
  margin-bottom: 5px;
}

.encouragement {
  background: #e8f5e8;
  color: #2e7d32;
  padding: 10px;
  border-radius: 6px;
  margin-top: 15px;
  text-align: center;
  font-weight: 500;
}

/* Modal Styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  padding: 0;
  border-radius: 12px;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
  padding: 20px 30px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-body {
  padding: 30px;
}

.modal-footer {
  padding: 20px 30px;
  border-top: 1px solid #eee;
  text-align: right;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #999;
}

.unlock-message {
  background: #e8f5e8;
  padding: 20px;
  border-radius: 8px;
  margin-top: 20px;
}

.unlock-message h4 {
  color: #2e7d32;
  margin-bottom: 10px;
}

/* Loading State */
.loading {
  text-align: center;
  padding: 60px 20px;
  color: #666;
}

/* Responsive Design */
@media (max-width: 768px) {
  .main-content {
    grid-template-columns: 1fr;
  }

  .sidebar {
    order: 2;
  }

  .content {
    order: 1;
  }

  .editor-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .editor-controls {
    justify-content: center;
  }
}

/* Notifications */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: 8px;
  color: white;
  font-weight: 500;
  z-index: 10000;
  transform: translateX(400px);
  opacity: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(10px);
}

.notification.show {
  transform: translateX(0);
  opacity: 1;
}

.notification-success {
  background: linear-gradient(45deg, #4caf50, #45a049);
}

.notification-error {
  background: linear-gradient(45deg, #f44336, #d32f2f);
}

.notification-info {
  background: linear-gradient(
    45deg,
    var(--accent-primary),
    var(--accent-secondary)
  );
}

/* Code Editor Enhancements */
.code-editor {
  tab-size: 2;
  -moz-tab-size: 2;
}

/* Keyboard shortcuts help */
.editor-shortcuts {
  font-size: 0.8rem;
  color: var(--text-muted);
  margin-top: 8px;
  text-align: center;
}

.editor-shortcuts kbd {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 3px;
  padding: 2px 6px;
  font-size: 0.75rem;
  font-family: monospace;
}
