const fs = require("fs").promises;
const vm = require("vm");

async function readJsonFile(filePath) {
  try {
    const data = await fs.readFile(filePath, "utf8");
    return JSON.parse(data);
  } catch (error) {
    console.error(`Error reading ${filePath}:`, error);
    return null;
  }
}

function createSafeContext() {
  const context = {
    console: {
      log: (...args) => {
        context._output.push(args.map((arg) => String(arg)).join(" "));
      },
    },
    _output: [],
    _result: undefined,
    setTimeout: undefined,
    setInterval: undefined,
    require: undefined,
    process: undefined,
    global: undefined,
    Buffer: undefined,
  };

  return context;
}

async function executeCode(code, taskId, type = "task") {
  try {
    // Load task or project data
    let taskData;
    if (type === "project") {
      const projects = await readJsonFile("./data/projects.json");
      taskData = projects?.projects.find((p) => p.id === taskId);
    } else {
      const tasks = await readJsonFile("./data/tasks.json");
      taskData = tasks?.tasks.find((t) => t.id === parseInt(taskId));
    }

    if (!taskData) {
      return {
        success: false,
        error: "Task or project not found",
        output: [],
        testResults: [],
      };
    }

    // Create safe execution context
    const context = createSafeContext();

    // Execute the user's code
    try {
      vm.createContext(context);
      vm.runInContext(code, context, { timeout: 5000 });
    } catch (executionError) {
      return {
        success: false,
        error: executionError.message,
        output: context._output,
        testResults: [],
        errorType: "runtime",
      };
    }

    // Run test cases
    const testResults = [];
    let allTestsPassed = true;

    for (const testCase of taskData.testCases) {
      try {
        let testResult;

        if (type === "project") {
          // For projects, run the test code directly in the same context
          try {
            // Evaluate the test expression and capture the result
            const testCode = `_testResult = ${testCase.test}`;
            vm.runInContext(testCode, context, { timeout: 1000 });
            testResult = context._testResult;

            // Clean up the test result variable
            delete context._testResult;
          } catch (evalError) {
            throw new Error(`Test evaluation failed: ${evalError.message}`);
          }
        } else {
          // For tasks, call the function with test inputs
          const functionName = extractFunctionName(code);
          if (!functionName) {
            throw new Error("No function found in code");
          }

          const func = context[functionName];
          if (typeof func !== "function") {
            throw new Error(
              `Function '${functionName}' not found or not a function`
            );
          }

          testResult = func.apply(null, testCase.input);
        }

        const passed = testResult === testCase.expected;
        testResults.push({
          description: testCase.description,
          input: testCase.input,
          expected: testCase.expected,
          actual: testResult,
          passed: passed,
        });

        if (!passed) {
          allTestsPassed = false;
        }
      } catch (testError) {
        testResults.push({
          description: testCase.description,
          input: testCase.input || "N/A",
          expected: testCase.expected,
          actual: "Error: " + testError.message,
          passed: false,
          error: testError.message,
        });
        allTestsPassed = false;
      }
    }

    return {
      success: allTestsPassed,
      output: context._output,
      testResults: testResults,
      message: allTestsPassed
        ? "All tests passed! Great job!"
        : "Some tests failed. Check the results below.",
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
      output: [],
      testResults: [],
      errorType: "system",
    };
  }
}

function extractFunctionName(code) {
  // Simple regex to extract function name from function declaration
  const functionMatch = code.match(
    /function\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\(/
  );
  if (functionMatch) {
    return functionMatch[1];
  }

  // Try arrow function assigned to variable
  const arrowMatch = code.match(
    /(?:const|let|var)\s+([a-zA-Z_$][a-zA-Z0-9_$]*)\s*=\s*\(/
  );
  if (arrowMatch) {
    return arrowMatch[1];
  }

  return null;
}

module.exports = {
  executeCode,
  createSafeContext,
  extractFunctionName,
};
