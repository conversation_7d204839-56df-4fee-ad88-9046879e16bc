<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>JavaScript Learning Mentor</title>
    <link rel="stylesheet" href="style.css" />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css"
      rel="stylesheet"
    />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
  </head>
  <body>
    <div class="container">
      <!-- Header -->
      <header class="header">
        <div class="header-content">
          <h1>🚀 JavaScript Learning Mentor</h1>
          <p>Learn JavaScript step by step with AI-powered guidance</p>
        </div>
        <button class="theme-toggle" id="theme-toggle">
          <span class="theme-icon">🌙</span>
          <span class="theme-text">Dark</span>
        </button>
      </header>

      <!-- Progress Bar -->
      <div class="progress-section">
        <div class="progress-info">
          <span id="progress-text">Loading...</span>
          <span id="progress-stats"></span>
        </div>
        <div class="progress-bar">
          <div class="progress-fill" id="progress-fill"></div>
        </div>
      </div>

      <!-- Main Content -->
      <div class="main-content">
        <!-- Current Task Info -->
        <div class="current-task-info">
          <div class="task-navigation">
            <button id="prev-task" class="nav-btn" disabled>
              <span class="nav-icon">←</span>
              <span class="nav-text">Previous</span>
            </button>

            <div class="task-indicator">
              <div class="task-number">
                <span id="current-task-num">1</span>
                <span class="task-total"
                  >/ <span id="total-tasks">9</span></span
                >
              </div>
              <div class="task-type" id="current-task-type">Logic Building</div>
            </div>

            <button id="next-task" class="nav-btn">
              <span class="nav-text">Next</span>
              <span class="nav-icon">→</span>
            </button>
          </div>

          <div class="achievement-bar">
            <div class="achievement-fill" id="achievement-fill"></div>
            <div class="achievement-text">
              <span id="achievement-text">🎯 Master the Basics</span>
            </div>
          </div>
        </div>

        <!-- Content Area -->
        <main class="content">
          <!-- Task/Project Display -->
          <div id="task-content" class="task-content">
            <div class="loading">
              <h2>Loading your first task...</h2>
              <p>Get ready to start your JavaScript journey! 🌟</p>
            </div>
          </div>

          <!-- Code Editor -->
          <div class="editor-section">
            <div class="editor-header">
              <h3>💻 Code Editor</h3>
              <div class="editor-controls">
                <button id="run-code" class="btn btn-primary">
                  ▶️ Run Code
                </button>
                <button id="reset-code" class="btn btn-secondary">
                  🔄 Reset
                </button>
                <button id="format-code" class="btn btn-format">
                  🎨 Format
                </button>
                <button id="get-hint" class="btn btn-hint">💡 Hint</button>
              </div>
            </div>
            <textarea
              id="code-editor"
              class="code-editor"
              placeholder="Your JavaScript code goes here..."
            ></textarea>
            <div class="editor-shortcuts">
              💡 <kbd>Tab</kbd> to indent, <kbd>Shift+Tab</kbd> to unindent,
              <kbd>Ctrl+Shift+F</kbd> to format
            </div>
          </div>

          <!-- Results Section -->
          <div
            id="results-section"
            class="results-section"
            style="display: none"
          >
            <div class="results-header">
              <h3>📊 Results</h3>
            </div>
            <div id="results-content" class="results-content">
              <!-- Results will appear here -->
            </div>
          </div>

          <!-- Feedback Section -->
          <div
            id="feedback-section"
            class="feedback-section"
            style="display: none"
          >
            <div class="feedback-header">
              <h3>🤖 AI Mentor Feedback</h3>
            </div>
            <div id="feedback-content" class="feedback-content">
              <!-- Feedback will appear here -->
            </div>
          </div>
        </main>
      </div>
    </div>

    <!-- Success Modal -->
    <div id="success-modal" class="modal" style="display: none">
      <div class="modal-content">
        <div class="modal-header">
          <h2>🎉 Congratulations!</h2>
        </div>
        <div class="modal-body">
          <p id="success-message">Great job completing this task!</p>
          <div id="unlock-message" class="unlock-message" style="display: none">
            <h4>🔓 New Content Unlocked!</h4>
            <div id="unlocked-content"></div>
          </div>
        </div>
        <div class="modal-footer">
          <button id="continue-btn" class="btn btn-primary">
            Continue Learning
          </button>
        </div>
      </div>
    </div>

    <!-- Hint Modal -->
    <div id="hint-modal" class="modal" style="display: none">
      <div class="modal-content">
        <div class="modal-header">
          <h2>💡 Hint</h2>
          <button class="close-btn" onclick="closeHintModal()">&times;</button>
        </div>
        <div class="modal-body">
          <div id="hint-content"></div>
        </div>
      </div>
    </div>

    <script src="app.js"></script>
  </body>
</html>
