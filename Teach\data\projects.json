{"projects": [{"id": "project-1", "title": "Simple Calculator", "description": "Build a calculator that can add, subtract, and multiply two numbers.", "difficulty": "beginner", "category": "mini-project", "instructions": "Create a calculator object with methods for addition, subtraction, and multiplication. Use the functions you learned in the previous tasks!", "starterCode": "const calculator = {\n  // Add your methods here\n  add: function(a, b) {\n    // Use your addNumbers logic here\n  },\n  \n  subtract: function(a, b) {\n    // Use your subtractNumbers logic here\n  },\n  \n  multiply: function(a, b) {\n    // Use your multiplyNumbers logic here\n  }\n};\n\n// Test your calculator\nconsole.log('Calculator Tests:');\nconsole.log('5 + 3 =', calculator.add(5, 3));\nconsole.log('10 - 4 =', calculator.subtract(10, 4));\nconsole.log('6 * 7 =', calculator.multiply(6, 7));", "solution": "const calculator = {\n  add: function(a, b) {\n    return a + b;\n  },\n  \n  subtract: function(a, b) {\n    return a - b;\n  },\n  \n  multiply: function(a, b) {\n    return a * b;\n  }\n};\n\n// Test your calculator\nconsole.log('Calculator Tests:');\nconsole.log('5 + 3 =', calculator.add(5, 3));\nconsole.log('10 - 4 =', calculator.subtract(10, 4));\nconsole.log('6 * 7 =', calculator.multiply(6, 7));", "testCases": [{"description": "Calculator should have add method", "test": "typeof calculator.add === 'function'", "expected": true}, {"description": "Calculator should have subtract method", "test": "typeof calculator.subtract === 'function'", "expected": true}, {"description": "Calculator should have multiply method", "test": "typeof calculator.multiply === 'function'", "expected": true}, {"description": "Addition should work correctly", "test": "calculator.add(5, 3)", "expected": 8}, {"description": "Subtraction should work correctly", "test": "calculator.subtract(10, 4)", "expected": 6}, {"description": "Multiplication should work correctly", "test": "calculator.multiply(6, 7)", "expected": 42}], "hints": ["Remember the functions you created in the previous tasks", "Each method should return the result of the operation", "Test your calculator with different numbers to make sure it works"], "concepts": ["objects", "methods", "functions", "arithmetic operations"], "prerequisiteTasks": [1, 2, 3], "unlocksNext": ["task-4", "task-5", "task-6"]}, {"id": "project-2", "title": "Smart Decision Maker", "description": "Build a decision-making system that uses conditionals to make smart choices.", "difficulty": "beginner", "category": "mini-project", "instructions": "Create a decision maker that can determine the maximum of three numbers, check if someone can vote (age >= 18), and assign letter grades to scores.", "starterCode": "const decisionMaker = {\n  // Find the maximum of three numbers\n  findMaxOfThree: function(a, b, c) {\n    // Use your conditional logic here\n  },\n  \n  // Check if someone can vote\n  canVote: function(age) {\n    // Return true if age >= 18, false otherwise\n  },\n  \n  // Get letter grade from score\n  getLetterGrade: function(score) {\n    // Use your grade calculation logic\n  },\n  \n  // Bonus: Determine if a year is a leap year\n  isLeapYear: function(year) {\n    // A year is leap if divisible by 4, except century years must be divisible by 400\n  }\n};\n\n// Test your decision maker\nconsole.log('Max of 5, 8, 3:', decisionMaker.findMaxOfThree(5, 8, 3));\nconsole.log('Can 17 year old vote?', decisionMaker.canVote(17));\nconsole.log('Grade for 85:', decisionMaker.getLetterGrade(85));\nconsole.log('Is 2024 a leap year?', decisionMaker.isLeapYear(2024));", "solution": "const decisionMaker = {\n  findMaxOfThree: function(a, b, c) {\n    if (a >= b && a >= c) {\n      return a;\n    } else if (b >= a && b >= c) {\n      return b;\n    } else {\n      return c;\n    }\n  },\n  \n  canVote: function(age) {\n    return age >= 18;\n  },\n  \n  getLetterGrade: function(score) {\n    if (score >= 90) return 'A';\n    else if (score >= 80) return 'B';\n    else if (score >= 70) return 'C';\n    else if (score >= 60) return 'D';\n    else return 'F';\n  },\n  \n  isLeapYear: function(year) {\n    return (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0);\n  }\n};\n\nconsole.log('Max of 5, 8, 3:', decisionMaker.findMaxOfThree(5, 8, 3));\nconsole.log('Can 17 year old vote?', decisionMaker.canVote(17));\nconsole.log('Grade for 85:', decisionMaker.getLetterGrade(85));\nconsole.log('Is 2024 a leap year?', decisionMaker.isLeapYear(2024));", "testCases": [{"description": "Should find maximum of three numbers", "test": "decisionMaker.findMaxOfThree(5, 8, 3)", "expected": 8}, {"description": "Should check voting eligibility correctly", "test": "decisionMaker.canVote(17)", "expected": false}, {"description": "Should assign correct letter grade", "test": "decisionMaker.getLetterGrade(85)", "expected": "B"}, {"description": "Should identify leap years correctly", "test": "decisionMaker.isLeapYear(2024)", "expected": true}], "hints": ["Use multiple if-else statements for complex logic", "Break down complex problems into smaller parts", "Test each method individually before combining them"], "concepts": ["complex conditionals", "logical operators", "multiple comparisons"], "prerequisiteTasks": [4, 5, 6], "unlocksNext": ["task-7", "task-8", "task-9"]}, {"id": "project-3", "title": "Number Pattern Generator", "description": "Create a system that generates various number patterns using loops.", "difficulty": "intermediate", "category": "mini-project", "instructions": "Build a pattern generator that can create sequences, count numbers, and generate mathematical patterns.", "starterCode": "const patternGenerator = {\n  // Generate numbers from 1 to n\n  countUp: function(n) {\n    const result = [];\n    // Use a loop to add numbers 1 to n\n    return result;\n  },\n  \n  // Generate even numbers up to n\n  evenNumbers: function(n) {\n    const result = [];\n    // Generate even numbers from 2 to n\n    return result;\n  },\n  \n  // Generate multiplication table\n  multiplicationTable: function(number, limit) {\n    const result = [];\n    // Generate multiplication table for 'number' up to 'limit'\n    return result;\n  },\n  \n  // Sum all numbers from 1 to n\n  sumRange: function(n) {\n    let sum = 0;\n    // Calculate sum of 1 + 2 + 3 + ... + n\n    return sum;\n  }\n};\n\n// Test your pattern generator\nconsole.log('Count up to 5:', patternGenerator.countUp(5));\nconsole.log('Even numbers up to 10:', patternGenerator.evenNumbers(10));\nconsole.log('5 times table:', patternGenerator.multiplicationTable(5, 10));\nconsole.log('Sum 1 to 10:', patternGenerator.sumRange(10));", "solution": "const patternGenerator = {\n  countUp: function(n) {\n    const result = [];\n    for (let i = 1; i <= n; i++) {\n      result.push(i);\n    }\n    return result;\n  },\n  \n  evenNumbers: function(n) {\n    const result = [];\n    for (let i = 2; i <= n; i += 2) {\n      result.push(i);\n    }\n    return result;\n  },\n  \n  multiplicationTable: function(number, limit) {\n    const result = [];\n    for (let i = 1; i <= limit; i++) {\n      result.push(number * i);\n    }\n    return result;\n  },\n  \n  sumRange: function(n) {\n    let sum = 0;\n    for (let i = 1; i <= n; i++) {\n      sum += i;\n    }\n    return sum;\n  }\n};\n\nconsole.log('Count up to 5:', patternGenerator.countUp(5));\nconsole.log('Even numbers up to 10:', patternGenerator.evenNumbers(10));\nconsole.log('5 times table:', patternGenerator.multiplicationTable(5, 10));\nconsole.log('Sum 1 to 10:', patternGenerator.sumRange(10));", "testCases": [{"description": "Should count up to 5", "test": "JSON.stringify(patternGenerator.countUp(5))", "expected": "[1,2,3,4,5]"}, {"description": "Should generate even numbers", "test": "JSON.stringify(patternGenerator.evenNumbers(8))", "expected": "[2,4,6,8]"}, {"description": "Should create multiplication table", "test": "JSON.stringify(patternGenerator.multiplicationTable(3, 4))", "expected": "[3,6,9,12]"}, {"description": "Should sum range correctly", "test": "patternGenerator.sumRange(5)", "expected": 15}], "hints": ["Use for loops to iterate through numbers", "Push results into arrays for list generation", "Use += to accumulate sums"], "concepts": ["for loops", "arrays", "iteration", "accumulation"], "prerequisiteTasks": [7, 8, 9], "unlocksNext": []}, {"id": "project-4", "title": "Data Analyzer", "description": "Build a comprehensive data analysis tool that processes arrays of numbers.", "difficulty": "intermediate", "category": "mini-project", "instructions": "Create a data analyzer that can find statistics, filter data, and generate reports from arrays of numbers.", "starterCode": "const dataAnalyzer = {\n  // Calculate basic statistics\n  getStats: function(numbers) {\n    return {\n      count: numbers.length,\n      sum: 0, // Calculate sum\n      average: 0, // Calculate average\n      min: 0, // Find minimum\n      max: 0 // Find maximum\n    };\n  },\n  \n  // Filter even numbers\n  getEvenNumbers: function(numbers) {\n    const evens = [];\n    // Filter and return only even numbers\n    return evens;\n  },\n  \n  // Find numbers above average\n  getAboveAverage: function(numbers) {\n    const average = 0; // Calculate average first\n    const aboveAvg = [];\n    // Find numbers above average\n    return aboveAvg;\n  },\n  \n  // Generate a simple report\n  generateReport: function(numbers) {\n    const stats = this.getStats(numbers);\n    const evens = this.getEvenNumbers(numbers);\n    const aboveAvg = this.getAboveAverage(numbers);\n    \n    return {\n      originalData: numbers,\n      statistics: stats,\n      evenNumbers: evens,\n      aboveAverageNumbers: aboveAvg,\n      summary: `Analyzed ${stats.count} numbers. Average: ${stats.average}, Range: ${stats.min}-${stats.max}`\n    };\n  }\n};\n\n// Test your data analyzer\nconst testData = [12, 7, 3, 15, 8, 22, 1, 9];\nconsole.log('Data Analysis Report:');\nconsole.log(dataAnalyzer.generateReport(testData));", "solution": "const dataAnalyzer = {\n  getStats: function(numbers) {\n    let sum = 0;\n    let min = numbers[0];\n    let max = numbers[0];\n    \n    for (let i = 0; i < numbers.length; i++) {\n      sum += numbers[i];\n      if (numbers[i] < min) min = numbers[i];\n      if (numbers[i] > max) max = numbers[i];\n    }\n    \n    return {\n      count: numbers.length,\n      sum: sum,\n      average: sum / numbers.length,\n      min: min,\n      max: max\n    };\n  },\n  \n  getEvenNumbers: function(numbers) {\n    const evens = [];\n    for (let i = 0; i < numbers.length; i++) {\n      if (numbers[i] % 2 === 0) {\n        evens.push(numbers[i]);\n      }\n    }\n    return evens;\n  },\n  \n  getAboveAverage: function(numbers) {\n    const stats = this.getStats(numbers);\n    const average = stats.average;\n    const aboveAvg = [];\n    \n    for (let i = 0; i < numbers.length; i++) {\n      if (numbers[i] > average) {\n        aboveAvg.push(numbers[i]);\n      }\n    }\n    return aboveAvg;\n  },\n  \n  generateReport: function(numbers) {\n    const stats = this.getStats(numbers);\n    const evens = this.getEvenNumbers(numbers);\n    const aboveAvg = this.getAboveAverage(numbers);\n    \n    return {\n      originalData: numbers,\n      statistics: stats,\n      evenNumbers: evens,\n      aboveAverageNumbers: aboveAvg,\n      summary: `Analyzed ${stats.count} numbers. Average: ${stats.average.toFixed(2)}, Range: ${stats.min}-${stats.max}`\n    };\n  }\n};\n\nconst testData = [12, 7, 3, 15, 8, 22, 1, 9];\nconsole.log('Data Analysis Report:');\nconsole.log(dataAnalyzer.generateReport(testData));", "testCases": [{"description": "Should calculate correct statistics", "test": "dataAnalyzer.getStats([1, 2, 3, 4, 5]).average", "expected": 3}, {"description": "Should filter even numbers correctly", "test": "JSON.stringify(dataAnalyzer.getEvenNumbers([1, 2, 3, 4, 5, 6]))", "expected": "[2,4,6]"}, {"description": "Should find numbers above average", "test": "JSON.stringify(dataAnalyzer.getAboveAverage([1, 2, 3, 4, 5]))", "expected": "[4,5]"}, {"description": "Should generate complete report", "test": "typeof dataAnalyzer.generateReport([1, 2, 3]).summary", "expected": "string"}], "hints": ["Break down complex problems into smaller functions", "Use loops to process arrays efficiently", "Combine multiple functions to create powerful tools", "Test each function individually before combining them"], "concepts": ["data processing", "statistics", "filtering", "complex algorithms"], "prerequisiteTasks": [7, 8, 9], "unlocksNext": []}]}