{"projects": [{"id": "project-1", "title": "Simple Calculator", "description": "Build a calculator that can add, subtract, and multiply two numbers.", "difficulty": "beginner", "category": "mini-project", "instructions": "Create a calculator object with methods for addition, subtraction, and multiplication. Use the functions you learned in the previous tasks!", "starterCode": "const calculator = {\n  // Add your methods here\n  add: function(a, b) {\n    // Use your addNumbers logic here\n  },\n  \n  subtract: function(a, b) {\n    // Use your subtractNumbers logic here\n  },\n  \n  multiply: function(a, b) {\n    // Use your multiplyNumbers logic here\n  }\n};\n\n// Test your calculator\nconsole.log('Calculator Tests:');\nconsole.log('5 + 3 =', calculator.add(5, 3));\nconsole.log('10 - 4 =', calculator.subtract(10, 4));\nconsole.log('6 * 7 =', calculator.multiply(6, 7));", "solution": "const calculator = {\n  add: function(a, b) {\n    return a + b;\n  },\n  \n  subtract: function(a, b) {\n    return a - b;\n  },\n  \n  multiply: function(a, b) {\n    return a * b;\n  }\n};\n\n// Test your calculator\nconsole.log('Calculator Tests:');\nconsole.log('5 + 3 =', calculator.add(5, 3));\nconsole.log('10 - 4 =', calculator.subtract(10, 4));\nconsole.log('6 * 7 =', calculator.multiply(6, 7));", "testCases": [{"description": "Calculator should have add method", "test": "typeof calculator.add === 'function'", "expected": true}, {"description": "Calculator should have subtract method", "test": "typeof calculator.subtract === 'function'", "expected": true}, {"description": "Calculator should have multiply method", "test": "typeof calculator.multiply === 'function'", "expected": true}, {"description": "Addition should work correctly", "test": "calculator.add(5, 3)", "expected": 8}, {"description": "Subtraction should work correctly", "test": "calculator.subtract(10, 4)", "expected": 6}, {"description": "Multiplication should work correctly", "test": "calculator.multiply(6, 7)", "expected": 42}], "hints": ["Remember the functions you created in the previous tasks", "Each method should return the result of the operation", "Test your calculator with different numbers to make sure it works"], "concepts": ["objects", "methods", "functions", "arithmetic operations"], "prerequisiteTasks": [1, 2, 3], "unlocksNext": ["task-4", "task-5", "task-6"]}]}