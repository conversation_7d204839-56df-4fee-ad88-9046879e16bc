const fs = require('fs').promises;

async function readJsonFile(filePath) {
  try {
    const data = await fs.readFile(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`Error reading ${filePath}:`, error);
    return null;
  }
}

async function writeJsonFile(filePath, data) {
  try {
    await fs.writeFile(filePath, JSON.stringify(data, null, 2));
    return true;
  } catch (error) {
    console.error(`Error writing ${filePath}:`, error);
    return false;
  }
}

async function completeTask(taskId) {
  try {
    const progress = await readJsonFile('./data/progress.json');
    const tasks = await readJsonFile('./data/tasks.json');
    const projects = await readJsonFile('./data/projects.json');
    
    if (!progress || !tasks || !projects) {
      throw new Error('Failed to load required data files');
    }

    // Mark task as completed
    if (!progress.completedTasks.includes(taskId)) {
      progress.completedTasks.push(taskId);
      progress.stats.totalTasksCompleted++;
      progress.stats.lastCompletionDate = new Date().toISOString();
      progress.user.lastActivity = new Date().toISOString();
      
      if (!progress.user.startDate) {
        progress.user.startDate = new Date().toISOString();
      }
    }

    // Find the completed task
    const completedTask = tasks.tasks.find(t => t.id === taskId);
    if (!completedTask) {
      throw new Error('Task not found');
    }

    // Unlock next tasks
    const nextTaskId = taskId + 1;
    const nextTask = tasks.tasks.find(t => t.id === nextTaskId);
    if (nextTask && !progress.unlockedTasks.includes(nextTaskId)) {
      progress.unlockedTasks.push(nextTaskId);
    }

    // Check if any projects should be unlocked
    const unlockedProjects = [];
    if (completedTask.unlocks) {
      for (const unlockId of completedTask.unlocks) {
        if (!progress.unlockedProjects.includes(unlockId)) {
          const project = projects.projects.find(p => p.id === unlockId);
          if (project) {
            // Check if all prerequisite tasks are completed
            const allPrereqsCompleted = project.prerequisiteTasks.every(
              prereqId => progress.completedTasks.includes(prereqId)
            );
            
            if (allPrereqsCompleted) {
              progress.unlockedProjects.push(unlockId);
              unlockedProjects.push(project);
            }
          }
        }
      }
    }

    // Update current task
    if (nextTask && progress.currentTask === taskId) {
      progress.currentTask = nextTaskId;
    }

    // Save progress
    const success = await writeJsonFile('./data/progress.json', progress);
    if (!success) {
      throw new Error('Failed to save progress');
    }

    return {
      success: true,
      progress: progress,
      unlockedProjects: unlockedProjects,
      nextTask: nextTask
    };

  } catch (error) {
    console.error('Error completing task:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

async function completeProject(projectId) {
  try {
    const progress = await readJsonFile('./data/progress.json');
    const projects = await readJsonFile('./data/projects.json');
    
    if (!progress || !projects) {
      throw new Error('Failed to load required data files');
    }

    // Mark project as completed
    if (!progress.completedProjects.includes(projectId)) {
      progress.completedProjects.push(projectId);
      progress.stats.totalProjectsCompleted++;
      progress.stats.lastCompletionDate = new Date().toISOString();
      progress.user.lastActivity = new Date().toISOString();
    }

    // Find the completed project
    const completedProject = projects.projects.find(p => p.id === projectId);
    if (!completedProject) {
      throw new Error('Project not found');
    }

    // Unlock next tasks if specified
    const unlockedTasks = [];
    if (completedProject.unlocksNext) {
      for (const unlockId of completedProject.unlocksNext) {
        if (typeof unlockId === 'string' && unlockId.startsWith('task-')) {
          const taskId = parseInt(unlockId.replace('task-', ''));
          if (!progress.unlockedTasks.includes(taskId)) {
            progress.unlockedTasks.push(taskId);
            unlockedTasks.push(taskId);
          }
        }
      }
    }

    // Save progress
    const success = await writeJsonFile('./data/progress.json', progress);
    if (!success) {
      throw new Error('Failed to save progress');
    }

    return {
      success: true,
      progress: progress,
      unlockedTasks: unlockedTasks
    };

  } catch (error) {
    console.error('Error completing project:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

async function getAvailableTasks() {
  try {
    const progress = await readJsonFile('./data/progress.json');
    const tasks = await readJsonFile('./data/tasks.json');
    
    if (!progress || !tasks) {
      throw new Error('Failed to load required data files');
    }

    const availableTasks = tasks.tasks.filter(task => 
      progress.unlockedTasks.includes(task.id) && 
      !progress.completedTasks.includes(task.id)
    );

    const completedTasks = tasks.tasks.filter(task => 
      progress.completedTasks.includes(task.id)
    );

    return {
      available: availableTasks,
      completed: completedTasks,
      current: availableTasks.find(task => task.id === progress.currentTask) || availableTasks[0]
    };

  } catch (error) {
    console.error('Error getting available tasks:', error);
    return {
      available: [],
      completed: [],
      current: null
    };
  }
}

async function getAvailableProjects() {
  try {
    const progress = await readJsonFile('./data/progress.json');
    const projects = await readJsonFile('./data/projects.json');
    
    if (!progress || !projects) {
      throw new Error('Failed to load required data files');
    }

    const availableProjects = projects.projects.filter(project => 
      progress.unlockedProjects.includes(project.id) && 
      !progress.completedProjects.includes(project.id)
    );

    const completedProjects = projects.projects.filter(project => 
      progress.completedProjects.includes(project.id)
    );

    return {
      available: availableProjects,
      completed: completedProjects
    };

  } catch (error) {
    console.error('Error getting available projects:', error);
    return {
      available: [],
      completed: []
    };
  }
}

module.exports = {
  completeTask,
  completeProject,
  getAvailableTasks,
  getAvailableProjects
};
