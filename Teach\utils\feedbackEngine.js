const fs = require("fs").promises;

async function readJsonFile(filePath) {
  try {
    const data = await fs.readFile(filePath, "utf8");
    return JSON.parse(data);
  } catch (error) {
    console.error(`Error reading ${filePath}:`, error);
    return null;
  }
}

const errorPatterns = {
  syntax: {
    patterns: [
      /Unexpected token/i,
      /Unexpected end of input/i,
      /Missing \) after argument list/i,
      /Missing \} after function body/i,
      /Unexpected identifier/i,
    ],
    feedback: {
      title: "Syntax Error",
      explanation:
        "There's a problem with how your code is written. JavaScript has specific rules for syntax.",
      commonCauses: [
        "Missing parentheses () or curly braces {}",
        "Missing semicolons ;",
        "Typos in keywords like 'function' or 'return'",
        "Unclosed strings or comments",
      ],
      tips: [
        "Check that all opening brackets have closing brackets",
        "Make sure function names are spelled correctly",
        "Use your code editor's syntax highlighting to spot errors",
      ],
    },
  },

  reference: {
    patterns: [
      /is not defined/i,
      /Cannot read property/i,
      /Cannot read properties of undefined/i,
      /is not a function/i,
    ],
    feedback: {
      title: "Reference Error",
      explanation:
        "You're trying to use something that doesn't exist or isn't available.",
      commonCauses: [
        "Misspelled variable or function names",
        "Using a variable before declaring it",
        "Calling a function that doesn't exist",
      ],
      tips: [
        "Check the spelling of your variable and function names",
        "Make sure you've declared all variables before using them",
        "Verify that function names match exactly",
      ],
    },
  },

  type: {
    patterns: [
      /Cannot read property .* of null/i,
      /Cannot read property .* of undefined/i,
      /is not a function/i,
      /Cannot convert undefined or null/i,
    ],
    feedback: {
      title: "Type Error",
      explanation:
        "You're trying to use a value in a way that doesn't match its type.",
      commonCauses: [
        "Trying to call something that isn't a function",
        "Accessing properties of null or undefined",
        "Using the wrong data type for an operation",
      ],
      tips: [
        "Check that your variables contain the expected values",
        "Use console.log() to see what your variables actually contain",
        "Make sure functions return the right type of data",
      ],
    },
  },
};

const conceptualFeedback = {
  functions: {
    missingReturn: {
      pattern: /function.*\{[^}]*\}/s,
      check: (code) => !code.includes("return"),
      feedback: {
        title: "Missing Return Statement",
        explanation:
          "Your function doesn't return anything! Functions need to give back a result.",
        example:
          "function addNumbers(a, b) {\n  return a + b; // Don't forget this!\n}",
        tip: "Always use 'return' to send a value back from your function.",
      },
    },

    wrongParameters: {
      pattern: /function\s+\w+\s*\([^)]*\)/,
      feedback: {
        title: "Check Your Parameters",
        explanation:
          "Make sure your function accepts the right number of parameters.",
        tip: "Look at the test cases to see how many parameters your function should accept.",
      },
    },
  },

  arithmetic: {
    wrongOperator: {
      feedback: {
        title: "Wrong Arithmetic Operator",
        explanation: "You might be using the wrong mathematical operator.",
        operators: {
          "+": "Addition - adds two numbers together",
          "-": "Subtraction - subtracts the second number from the first",
          "*": "Multiplication - multiplies two numbers",
          "/": "Division - divides the first number by the second",
        },
        tip: "Double-check which operation the task is asking for.",
      },
    },
  },
};

async function provideFeedback(error, code, taskId, type = "task") {
  try {
    // Load task data for context
    let taskData;
    if (type === "project") {
      const projects = await readJsonFile("./data/projects.json");
      taskData = projects?.projects.find((p) => p.id === taskId);
    } else {
      const tasks = await readJsonFile("./data/tasks.json");
      taskData = tasks?.tasks.find((t) => t.id === parseInt(taskId));
    }

    const feedback = {
      type: "error",
      title: "Let's Fix This Together!",
      message: "",
      explanation: "",
      tips: [],
      example: null,
      hints: taskData?.hints || [],
    };

    // Analyze error type
    let errorType = "unknown";
    let errorFeedback = null;

    for (const [type, config] of Object.entries(errorPatterns)) {
      if (config.patterns.some((pattern) => pattern.test(error))) {
        errorType = type;
        errorFeedback = config.feedback;
        break;
      }
    }

    if (errorFeedback) {
      feedback.title = errorFeedback.title;
      feedback.explanation = errorFeedback.explanation;
      feedback.tips = errorFeedback.tips;

      if (errorFeedback.commonCauses) {
        feedback.message = `Common causes: ${errorFeedback.commonCauses.join(
          ", "
        )}`;
      }
    }

    // Add conceptual feedback based on task type
    if (taskData) {
      const conceptualTips = analyzeConceptualIssues(code, taskData);
      if (conceptualTips.length > 0) {
        feedback.tips = [...feedback.tips, ...conceptualTips];
      }
    }

    // Add encouraging message
    feedback.encouragement = getEncouragingMessage(errorType);

    // Add specific debugging steps
    feedback.debuggingSteps = getDebuggingSteps(code, taskData, errorType);

    // Add code suggestions if possible
    feedback.suggestion = getCodeSuggestion(code, taskData, errorType);

    return feedback;
  } catch (err) {
    console.error("Error providing feedback:", err);
    return {
      type: "error",
      title: "Oops!",
      message: "Something went wrong, but don't give up!",
      explanation:
        "There was an error in your code. Try checking the syntax and logic.",
      tips: [
        "Check for typos",
        "Make sure all brackets are closed",
        "Verify your function logic",
      ],
      encouragement: "Every programmer makes mistakes - it's how we learn! 🚀",
    };
  }
}

function analyzeConceptualIssues(code, taskData) {
  const tips = [];

  // Check for missing return statement
  if (code.includes("function") && !code.includes("return")) {
    tips.push("Don't forget to return a value from your function!");
  }

  // Check for common arithmetic mistakes
  if (taskData.concepts?.includes("addition") && !code.includes("+")) {
    tips.push("For addition, use the + operator");
  }

  if (taskData.concepts?.includes("subtraction") && !code.includes("-")) {
    tips.push("For subtraction, use the - operator");
  }

  if (taskData.concepts?.includes("multiplication") && !code.includes("*")) {
    tips.push("For multiplication, use the * operator");
  }

  return tips;
}

function getEncouragingMessage(errorType) {
  const messages = [
    "Every expert was once a beginner! 🌟",
    "Debugging is like being a detective - you've got this! 🔍",
    "Mistakes are proof that you're trying! Keep going! 💪",
    "The best programmers are the ones who never give up! 🚀",
    "Each error teaches you something new! 📚",
    "You're building your programming skills one line at a time! ⭐",
  ];

  return messages[Math.floor(Math.random() * messages.length)];
}

function getDebuggingSteps(code, taskData, errorType) {
  const steps = [];

  if (errorType === "syntax") {
    steps.push("1. 🔍 Look for missing brackets: ( ) { } [ ]");
    steps.push("2. ✅ Check that every opening bracket has a closing bracket");
    steps.push(
      "3. 📝 Verify function and variable names are spelled correctly"
    );
    steps.push(
      "4. 🎨 Use the Format button to see if code structure looks right"
    );
  } else if (errorType === "reference") {
    steps.push("1. 🔤 Check spelling of all variable and function names");
    steps.push(
      "2. 📋 Make sure you're using the parameter names from the function definition"
    );
    steps.push(
      "3. 🔍 Look at the task instructions for the exact function name required"
    );
  } else if (errorType === "type") {
    steps.push("1. 🧪 Add console.log() to see what your variables contain");
    steps.push(
      "2. 🔢 Check that you're using the right data types (numbers, strings, arrays)"
    );
    steps.push("3. ⚙️ Verify that you're calling functions correctly with ()");
  } else {
    steps.push("1. 📖 Re-read the task instructions carefully");
    steps.push("2. 🎯 Check that your function name matches exactly");
    steps.push("3. 🔄 Make sure you're returning the right type of value");
    steps.push("4. 🧪 Test with simple values first");
  }

  return steps;
}

function getCodeSuggestion(code, taskData, errorType) {
  if (!taskData) return null;

  // Suggest based on task type
  if (
    taskData.title?.includes("Add") ||
    taskData.concepts?.includes("addition")
  ) {
    return {
      title: "💡 Code Structure Hint",
      code: `function ${
        taskData.title?.includes("Add") ? "addNumbers" : "yourFunction"
      }(a, b) {
  // Use the + operator for addition
  return a + b;
}`,
    };
  }

  if (
    taskData.title?.includes("Multiply") ||
    taskData.concepts?.includes("multiplication")
  ) {
    return {
      title: "💡 Code Structure Hint",
      code: `function multiplyNumbers(a, b) {
  // Use the * operator for multiplication
  return a * b;
}`,
    };
  }

  if (
    taskData.concepts?.includes("conditionals") ||
    taskData.concepts?.includes("if-else")
  ) {
    return {
      title: "💡 Code Structure Hint",
      code: `function yourFunction(value) {
  if (condition) {
    return something;
  } else {
    return somethingElse;
  }
}`,
    };
  }

  if (
    taskData.concepts?.includes("loops") ||
    taskData.concepts?.includes("for loops")
  ) {
    return {
      title: "💡 Code Structure Hint",
      code: `function yourFunction(array) {
  for (let i = 0; i < array.length; i++) {
    // Do something with array[i]
  }
  return result;
}`,
    };
  }

  return null;
}

module.exports = {
  provideFeedback,
  errorPatterns,
  conceptualFeedback,
};
