const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs').promises;

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Utility functions for data management
async function readJsonFile(filePath) {
  try {
    const data = await fs.readFile(filePath, 'utf8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`Error reading ${filePath}:`, error);
    return null;
  }
}

async function writeJsonFile(filePath, data) {
  try {
    await fs.writeFile(filePath, JSON.stringify(data, null, 2));
    return true;
  } catch (error) {
    console.error(`Error writing ${filePath}:`, error);
    return false;
  }
}

// API Routes

// Get all tasks
app.get('/api/tasks', async (req, res) => {
  const tasks = await readJsonFile('./data/tasks.json');
  if (tasks) {
    res.json(tasks);
  } else {
    res.status(500).json({ error: 'Failed to load tasks' });
  }
});

// Get specific task
app.get('/api/tasks/:id', async (req, res) => {
  const tasks = await readJsonFile('./data/tasks.json');
  if (tasks) {
    const task = tasks.tasks.find(t => t.id === parseInt(req.params.id));
    if (task) {
      res.json(task);
    } else {
      res.status(404).json({ error: 'Task not found' });
    }
  } else {
    res.status(500).json({ error: 'Failed to load tasks' });
  }
});

// Get all projects
app.get('/api/projects', async (req, res) => {
  const projects = await readJsonFile('./data/projects.json');
  if (projects) {
    res.json(projects);
  } else {
    res.status(500).json({ error: 'Failed to load projects' });
  }
});

// Get specific project
app.get('/api/projects/:id', async (req, res) => {
  const projects = await readJsonFile('./data/projects.json');
  if (projects) {
    const project = projects.projects.find(p => p.id === req.params.id);
    if (project) {
      res.json(project);
    } else {
      res.status(404).json({ error: 'Project not found' });
    }
  } else {
    res.status(500).json({ error: 'Failed to load projects' });
  }
});

// Get user progress
app.get('/api/progress', async (req, res) => {
  const progress = await readJsonFile('./data/progress.json');
  if (progress) {
    res.json(progress);
  } else {
    res.status(500).json({ error: 'Failed to load progress' });
  }
});

// Update user progress
app.post('/api/progress', async (req, res) => {
  const currentProgress = await readJsonFile('./data/progress.json');
  if (currentProgress) {
    const updatedProgress = { ...currentProgress, ...req.body };
    const success = await writeJsonFile('./data/progress.json', updatedProgress);
    if (success) {
      res.json(updatedProgress);
    } else {
      res.status(500).json({ error: 'Failed to save progress' });
    }
  } else {
    res.status(500).json({ error: 'Failed to load current progress' });
  }
});

// Execute code endpoint
app.post('/api/execute', async (req, res) => {
  const { code, taskId, type } = req.body;
  
  try {
    // Import our code execution utilities
    const { executeCode } = require('./utils/codeRunner');
    const { provideFeedback } = require('./utils/feedbackEngine');
    
    const result = await executeCode(code, taskId, type);
    
    if (!result.success && result.error) {
      const feedback = await provideFeedback(result.error, code, taskId, type);
      result.feedback = feedback;
    }
    
    res.json(result);
  } catch (error) {
    console.error('Execution error:', error);
    res.status(500).json({ 
      success: false, 
      error: 'Server error during code execution',
      message: error.message 
    });
  }
});

// Serve the main application
app.get('/', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 JavaScript Learning Mentor Server running on http://localhost:${PORT}`);
  console.log(`📚 Ready to help you learn JavaScript step by step!`);
});

module.exports = app;
