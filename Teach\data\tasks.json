{"tasks": [{"id": 1, "title": "Add Two Numbers", "description": "Write a function that takes two numbers and returns their sum.", "difficulty": "beginner", "category": "basic-operations", "instructions": "Create a function called 'addNumbers' that takes two parameters (a and b) and returns their sum.", "starterCode": "function addNumbers(a, b) {\n  // Write your code here\n  \n}", "solution": "function addNumbers(a, b) {\n  return a + b;\n}", "testCases": [{"input": [2, 3], "expected": 5, "description": "addNumbers(2, 3) should return 5"}, {"input": [10, 15], "expected": 25, "description": "addNumbers(10, 15) should return 25"}, {"input": [-5, 3], "expected": -2, "description": "addNumbers(-5, 3) should return -2"}], "hints": ["Use the + operator to add two numbers", "Don't forget to return the result", "The function should work with both positive and negative numbers"], "concepts": ["functions", "parameters", "return", "addition"], "unlocks": [], "prerequisite": null}, {"id": 2, "title": "Subtract Two Numbers", "description": "Write a function that takes two numbers and returns their difference.", "difficulty": "beginner", "category": "basic-operations", "instructions": "Create a function called 'subtractNumbers' that takes two parameters (a and b) and returns a minus b.", "starterCode": "function subtractNumbers(a, b) {\n  // Write your code here\n  \n}", "solution": "function subtractNumbers(a, b) {\n  return a - b;\n}", "testCases": [{"input": [10, 3], "expected": 7, "description": "subtractNumbers(10, 3) should return 7"}, {"input": [5, 8], "expected": -3, "description": "subtractNumbers(5, 8) should return -3"}, {"input": [0, 5], "expected": -5, "description": "subtractNumbers(0, 5) should return -5"}], "hints": ["Use the - operator to subtract two numbers", "Remember that a - b is different from b - a", "The result can be negative"], "concepts": ["functions", "parameters", "return", "subtraction"], "unlocks": [], "prerequisite": 1}, {"id": 3, "title": "Multiply Two Numbers", "description": "Write a function that takes two numbers and returns their product.", "difficulty": "beginner", "category": "basic-operations", "instructions": "Create a function called 'multiplyNumbers' that takes two parameters (a and b) and returns their product.", "starterCode": "function multiplyNumbers(a, b) {\n  // Write your code here\n  \n}", "solution": "function multiplyNumbers(a, b) {\n  return a * b;\n}", "testCases": [{"input": [4, 5], "expected": 20, "description": "multiplyNumbers(4, 5) should return 20"}, {"input": [3, 7], "expected": 21, "description": "multiplyNumbers(3, 7) should return 21"}, {"input": [-2, 6], "expected": -12, "description": "multiplyNumbers(-2, 6) should return -12"}], "hints": ["Use the * operator to multiply two numbers", "Multiplying by a negative number gives a negative result", "Multiplying by zero gives zero"], "concepts": ["functions", "parameters", "return", "multiplication"], "unlocks": ["project-1"], "prerequisite": 2}, {"id": 4, "title": "Find Maximum Number", "description": "Write a function that finds the larger of two numbers.", "difficulty": "beginner", "category": "conditionals", "instructions": "Create a function called 'findMax' that takes two parameters and returns the larger number using if-else statements.", "starterCode": "function findMax(a, b) {\n  // Write your code here\n  \n}", "solution": "function findMax(a, b) {\n  if (a > b) {\n    return a;\n  } else {\n    return b;\n  }\n}", "testCases": [{"input": [5, 3], "expected": 5, "description": "findMax(5, 3) should return 5"}, {"input": [2, 8], "expected": 8, "description": "findMax(2, 8) should return 8"}, {"input": [7, 7], "expected": 7, "description": "find<PERSON><PERSON>(7, 7) should return 7"}], "hints": ["Use an if statement to compare the two numbers", "Return the larger number", "Consider what happens when both numbers are equal"], "concepts": ["conditionals", "if-else", "comparison operators"], "unlocks": [], "prerequisite": null}, {"id": 5, "title": "Check Even or Odd", "description": "Write a function that checks if a number is even or odd.", "difficulty": "beginner", "category": "conditionals", "instructions": "Create a function called 'isEven' that returns true if the number is even, false if odd.", "starterCode": "function isEven(number) {\n  // Write your code here\n  \n}", "solution": "function isEven(number) {\n  return number % 2 === 0;\n}", "testCases": [{"input": [4], "expected": true, "description": "isEven(4) should return true"}, {"input": [7], "expected": false, "description": "isEven(7) should return false"}, {"input": [0], "expected": true, "description": "isEven(0) should return true"}], "hints": ["Use the modulo operator (%) to check remainder", "Even numbers have remainder 0 when divided by 2", "Use === for comparison"], "concepts": ["modulo operator", "boolean return", "conditionals"], "unlocks": [], "prerequisite": 4}, {"id": 6, "title": "Grade Calculator", "description": "Write a function that converts a numeric score to a letter grade.", "difficulty": "beginner", "category": "conditionals", "instructions": "Create a function called 'getGrade' that returns 'A' for 90+, 'B' for 80-89, '<PERSON>' for 70-79, 'D' for 60-69, 'F' for below 60.", "starterCode": "function getGrade(score) {\n  // Write your code here\n  \n}", "solution": "function getGrade(score) {\n  if (score >= 90) {\n    return 'A';\n  } else if (score >= 80) {\n    return 'B';\n  } else if (score >= 70) {\n    return 'C';\n  } else if (score >= 60) {\n    return 'D';\n  } else {\n    return 'F';\n  }\n}", "testCases": [{"input": [95], "expected": "A", "description": "<PERSON><PERSON><PERSON>(95) should return 'A'"}, {"input": [85], "expected": "B", "description": "<PERSON><PERSON><PERSON>(85) should return 'B'"}, {"input": [55], "expected": "F", "description": "<PERSON><PERSON><PERSON>(55) should return 'F'"}], "hints": ["Use if-else if statements for multiple conditions", "Start with the highest grade first", "Remember to return strings, not variables"], "concepts": ["if-else if", "multiple conditions", "string return"], "unlocks": ["project-2"], "prerequisite": 5}, {"id": 7, "title": "Count to N", "description": "Write a function that counts from 1 to a given number using a loop.", "difficulty": "beginner", "category": "loops", "instructions": "Create a function called 'countToN' that takes a number and returns an array of numbers from 1 to that number.", "starterCode": "function countToN(n) {\n  // Write your code here\n  \n}", "solution": "function countToN(n) {\n  const result = [];\n  for (let i = 1; i <= n; i++) {\n    result.push(i);\n  }\n  return result;\n}", "testCases": [{"input": [5], "expected": [1, 2, 3, 4, 5], "description": "countToN(5) should return [1, 2, 3, 4, 5]"}, {"input": [3], "expected": [1, 2, 3], "description": "countToN(3) should return [1, 2, 3]"}, {"input": [1], "expected": [1], "description": "countToN(1) should return [1]"}], "hints": ["Use a for loop to iterate from 1 to n", "Create an empty array and push numbers into it", "Remember to return the array"], "concepts": ["for loops", "arrays", "push method"], "unlocks": [], "prerequisite": null}, {"id": 8, "title": "Sum Array Elements", "description": "Write a function that calculates the sum of all numbers in an array.", "difficulty": "beginner", "category": "loops", "instructions": "Create a function called 'sumArray' that takes an array of numbers and returns their sum.", "starterCode": "function sumArray(numbers) {\n  // Write your code here\n  \n}", "solution": "function sumArray(numbers) {\n  let sum = 0;\n  for (let i = 0; i < numbers.length; i++) {\n    sum += numbers[i];\n  }\n  return sum;\n}", "testCases": [{"input": [[1, 2, 3, 4, 5]], "expected": 15, "description": "sumArray([1, 2, 3, 4, 5]) should return 15"}, {"input": [[10, 20, 30]], "expected": 60, "description": "sumArray([10, 20, 30]) should return 60"}, {"input": [[5]], "expected": 5, "description": "sumArray([5]) should return 5"}], "hints": ["Initialize a sum variable to 0", "Use a for loop to iterate through the array", "Add each element to the sum using +="], "concepts": ["for loops", "arrays", "accumulation"], "unlocks": [], "prerequisite": 7}, {"id": 9, "title": "Find Largest Number", "description": "Write a function that finds the largest number in an array.", "difficulty": "beginner", "category": "loops", "instructions": "Create a function called 'findLargest' that takes an array of numbers and returns the largest one.", "starterCode": "function findLargest(numbers) {\n  // Write your code here\n  \n}", "solution": "function findLargest(numbers) {\n  let largest = numbers[0];\n  for (let i = 1; i < numbers.length; i++) {\n    if (numbers[i] > largest) {\n      largest = numbers[i];\n    }\n  }\n  return largest;\n}", "testCases": [{"input": [[3, 7, 2, 9, 1]], "expected": 9, "description": "find<PERSON>argest([3, 7, 2, 9, 1]) should return 9"}, {"input": [[15, 8, 23, 4]], "expected": 23, "description": "find<PERSON><PERSON>ges<PERSON>([15, 8, 23, 4]) should return 23"}, {"input": [[5, 5, 5]], "expected": 5, "description": "find<PERSON>argest([5, 5, 5]) should return 5"}], "hints": ["Start by assuming the first element is the largest", "Loop through the rest of the array", "Compare each element with the current largest"], "concepts": ["for loops", "arrays", "comparison", "tracking maximum"], "unlocks": ["project-3"], "prerequisite": 8}]}