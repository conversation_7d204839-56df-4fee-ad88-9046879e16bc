{"tasks": [{"id": 1, "title": "Add Two Numbers", "description": "Write a function that takes two numbers and returns their sum.", "difficulty": "beginner", "category": "basic-operations", "instructions": "Create a function called 'addNumbers' that takes two parameters (a and b) and returns their sum.", "starterCode": "function addNumbers(a, b) {\n  // Write your code here\n  \n}", "solution": "function addNumbers(a, b) {\n  return a + b;\n}", "testCases": [{"input": [2, 3], "expected": 5, "description": "addNumbers(2, 3) should return 5"}, {"input": [10, 15], "expected": 25, "description": "addNumbers(10, 15) should return 25"}, {"input": [-5, 3], "expected": -2, "description": "addNumbers(-5, 3) should return -2"}], "hints": ["Use the + operator to add two numbers", "Don't forget to return the result", "The function should work with both positive and negative numbers"], "concepts": ["functions", "parameters", "return", "addition"], "unlocks": [], "prerequisite": null}, {"id": 2, "title": "Subtract Two Numbers", "description": "Write a function that takes two numbers and returns their difference.", "difficulty": "beginner", "category": "basic-operations", "instructions": "Create a function called 'subtractNumbers' that takes two parameters (a and b) and returns a minus b.", "starterCode": "function subtractNumbers(a, b) {\n  // Write your code here\n  \n}", "solution": "function subtractNumbers(a, b) {\n  return a - b;\n}", "testCases": [{"input": [10, 3], "expected": 7, "description": "subtractNumbers(10, 3) should return 7"}, {"input": [5, 8], "expected": -3, "description": "subtractNumbers(5, 8) should return -3"}, {"input": [0, 5], "expected": -5, "description": "subtractNumbers(0, 5) should return -5"}], "hints": ["Use the - operator to subtract two numbers", "Remember that a - b is different from b - a", "The result can be negative"], "concepts": ["functions", "parameters", "return", "subtraction"], "unlocks": [], "prerequisite": 1}, {"id": 3, "title": "Multiply Two Numbers", "description": "Write a function that takes two numbers and returns their product.", "difficulty": "beginner", "category": "basic-operations", "instructions": "Create a function called 'multiplyNumbers' that takes two parameters (a and b) and returns their product.", "starterCode": "function multiplyNumbers(a, b) {\n  // Write your code here\n  \n}", "solution": "function multiplyNumbers(a, b) {\n  return a * b;\n}", "testCases": [{"input": [4, 5], "expected": 20, "description": "multiplyNumbers(4, 5) should return 20"}, {"input": [3, 7], "expected": 21, "description": "multiplyNumbers(3, 7) should return 21"}, {"input": [-2, 6], "expected": -12, "description": "multiplyNumbers(-2, 6) should return -12"}], "hints": ["Use the * operator to multiply two numbers", "Multiplying by a negative number gives a negative result", "Multiplying by zero gives zero"], "concepts": ["functions", "parameters", "return", "multiplication"], "unlocks": ["project-1"], "prerequisite": 2}]}